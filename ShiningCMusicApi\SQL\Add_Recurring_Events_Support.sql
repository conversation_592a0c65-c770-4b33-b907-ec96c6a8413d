USE [MusicSchool]
GO

-- Add missing columns for recurring events support
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'RecurrenceRule')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [RecurrenceRule] [nvarchar](500) NULL;
    PRINT 'Added RecurrenceRule column to Lessons table';
END
ELSE
BEGIN
    PRINT 'RecurrenceRule column already exists in Lessons table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'RecurrenceException')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [RecurrenceException] [nvarchar](1000) NULL;
    PRINT 'Added RecurrenceException column to Lessons table';
END
ELSE
BEGIN
    PRINT 'RecurrenceException column already exists in Lessons table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'RecurrenceID')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [RecurrenceID] [int] NULL;
    PRINT 'Added RecurrenceID column to Lessons table';
END
ELSE
BEGIN
    PRINT 'RecurrenceID column already exists in Lessons table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'IsRecurring')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [IsRecurring] [bit] NOT NULL DEFAULT 0;
    PRINT 'Added IsRecurring column to Lessons table';
END
ELSE
BEGIN
    PRINT 'IsRecurring column already exists in Lessons table';
END

-- Add foreign key constraint for RecurrenceID (self-referencing)
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Lessons_RecurrenceID')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD CONSTRAINT [FK_Lessons_RecurrenceID] 
    FOREIGN KEY ([RecurrenceID]) REFERENCES [dbo].[Lessons]([LessonId]);
    PRINT 'Added FK_Lessons_RecurrenceID foreign key constraint';
END
ELSE
BEGIN
    PRINT 'FK_Lessons_RecurrenceID foreign key constraint already exists';
END

-- Create index for better performance on recurring event queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_RecurrenceID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_RecurrenceID] ON [dbo].[Lessons]
    (
        [RecurrenceID] ASC
    ) WHERE [RecurrenceID] IS NOT NULL;
    PRINT 'Created index IX_Lessons_RecurrenceID';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_RecurrenceID already exists';
END

-- Create index for recurring events
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_IsRecurring')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_IsRecurring] ON [dbo].[Lessons]
    (
        [IsRecurring] ASC
    ) WHERE [IsRecurring] = 1;
    PRINT 'Created index IX_Lessons_IsRecurring';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_IsRecurring already exists';
END

PRINT 'Recurring events database schema update completed successfully';
GO
